#include "passivestatemachine.h"

#define ON 1
#define OFF 0

PassiveStateMachine::PassiveStateMachine(std::shared_ptr<SerialService> serial_port) 
{
    current_state_ = State::IDLE;
    serial_port_ = serial_port->createSerialPort();//创建串口
    serial_port_->openPort("COM3", 9600, 8);//todo:读取配置文件加载参数
}

PassiveStateMachine::~PassiveStateMachine() {}

std::string PassiveStateMachine::getCurrentStateName() const
{
    switch(current_state_) {
        case State::IDLE: return "IDLE";
        case State::P_WAITING_FOR_AGV_REQUEST_CMD: return "P_WAITING_FOR_AGV_REQUEST_CMD";
        case State::P_PREPARING_TRANSFER: return "P_PREPARING_TRANSFER";
        case State::P_READY_WAITING_FOR_AGV_BUSY: return "P_READY_WAITING_FOR_AGV_BUSY";
        case State::P_MONITORING_TRANSFER: return "P_MONITORING_TRANSFER";
        case State::P_WAITING_FOR_FINAL_AGV_CLEANUP: return "P_WAITING_FOR_FINAL_AGV_CLEANUP";
        case State::P_ERROR_STATE: return "P_ERROR_STATE";
        default: return "UNKNOWN_PASSIVE_STATE";
    }
}
// 辅助函数：将2位十六进制字符（代表一个字节）转换为8个bool值（代表8个通道/位）
// bit 0 of hex_value -> result[0] (Channel 0)
// bit 7 of hex_value -> result[7] (Channel 7)
std::vector<bool> PassiveStateMachine::hex_byte_string_to_8bits(const std::string& hex_byte_str) {
    if (hex_byte_str.length() != 2) {
        throw std::invalid_argument("Hex byte string must be 2 characters long. Got: '" + hex_byte_str + "'");
    }
    unsigned int byte_value;
    try {
        byte_value = std::stoul(hex_byte_str, nullptr, 16);
    } catch (const std::exception& e) {
        throw std::invalid_argument("Invalid hex character in string: '" + hex_byte_str + "' (" + e.what() + ")");
    }

    if (byte_value > 0xFF) { // 超出一个字节的范围
        throw std::out_of_range("Hex value out of 8-bit range: '" + hex_byte_str + "'");
    }

    std::vector<bool> bits(8);
    for (int i = 0; i < 8; ++i) {
        bits[i] = (byte_value >> i) & 1;
    }
    return bits;
}

/// @brief 用于解析查询所有IO状态
/// @param response_str_with_cr 模块返回的原始字符串
/// @return 包含解析结果的 Adam4055Data 结构体
Adam4055Data PassiveStateMachine::parse_adam_4055_response(const std::string& response_str_with_cr) {
    Adam4055Data result;
    result.rawResponse = response_str_with_cr;

    if (response_str_with_cr.empty()) {
        result.errorMessage = "Response string is empty.";
        return result;
    }

    std::string response_str = response_str_with_cr;
    // 移除末尾的回车符 '\r'
    if (response_str.back() == '\r') {
        response_str.pop_back();
    }

    if (response_str.empty()) {
        result.errorMessage = "Response string became empty after removing CR.";
        return result;
    }

    char indicator = response_str[0];

    // 检查响应头和基本长度
    if ((indicator != '!' && indicator != '?') || response_str.length() < 3) {
        result.errorMessage = "Invalid response format: Must start with '!' or '?' and contain an address. Got: '" + response_str + "'";
        return result;
    }

    result.moduleAddress = response_str.substr(1, 2);

    if (indicator == '?') {
        result.isErrorResponse = true;
        result.commandWasValid = false; //  '?' 表示模块认为命令无效
        result.errorMessage = "Module indicated an invalid command or error.";
        return result;
    }

    // 处理 '!' 开头的有效响应
    result.commandWasValid = true;
    // 预期数据部分在 "!AA" 之后，长度应为 (dataOutput:2) + (dataInput:2) + (suffix:2) = 6 个字符
    // 例如：!01(DataOut)(DataIn)00
    if (response_str.length() != 1 + 2 + 2 + 2 + 2) { // 1(!) + 2(AA) + 2(DO) + 2(DI) + 2(00) = 9
        result.errorMessage = "Invalid response length for ADAM-4055. Expected 9 chars before CR (e.g., !AADOODII00). Got: '" + response_str + "'";
        // 仍然尝试解析已知部分
    }

    std::string data_part = response_str.substr(3); // 获取 "!AA" 之后的部分

    if (data_part.length() < 4) { // 至少需要DO和DI数据
        result.errorMessage = (result.errorMessage.empty() ? "" : result.errorMessage + " ") + "Data part too short for DO and DI fields. Data part: '" + data_part + "'";
        return result;
    }

    try {
        // 解析 (dataOutput) - 2位十六进制字符代表8个DO
        result.outputStates = hex_byte_string_to_8bits(data_part.substr(0, 2));

        // 解析 (dataInput) - 2位十六进制字符代表8个DI
        result.inputStates = hex_byte_string_to_8bits(data_part.substr(2, 2));

        // 检查后缀 "00"
        if (data_part.length() >= 6) {
            if (data_part.substr(4, 2) != "00") {
                std::string warning = "Warning: Expected suffix '00', got '" + data_part.substr(4, 2) + "'.";
                result.errorMessage = result.errorMessage.empty() ? warning : result.errorMessage + " " + warning;
            }
        } else {
             std::string warning = "Warning: Suffix '00' is missing or data part is too short.";
             result.errorMessage = result.errorMessage.empty() ? warning : result.errorMessage + " " + warning;
        }

    } catch (const std::exception& e) {
        result.errorMessage = (result.errorMessage.empty() ? "" : result.errorMessage + " ") + "Exception during data parsing: " + e.what();
    }

    return result;
}

/**
 * @brief 校验 ADAM 模块的响应是否为成功确认符 ">(cr)".
 * * ADAM 模块在成功执行 "Digital Data Out" (如 #AADD(data)(cr)) 等设置指令后，
 * 会返回 ">(cr)"作为确认。
 * 此函数会检查输入字符串是否符合这个格式。
 * * @param response_str 模块返回的原始字符串，可能包含末尾的回车符。
 * @return 如果响应是有效的 ">(cr)" 确认符，则返回 true；否则返回 false。
 */
bool is_adam_output_ack(const std::string& response_str) {
    if (response_str.empty()) {
        return false;
    }

    std::string temp_response = response_str;

    // 1. 检查并移除末尾可能存在的回车符 '\r' (CR)
    //    有些通信库可能也会包含换行符 '\n' (LF)，这里只严格按 (cr) 处理
    if (temp_response.back() == '\r') {
        temp_response.pop_back();
    }

    // 2. 移除回车符后，剩下的字符串应该仅仅是 ">"
    return temp_response == ">";
}

/// @brief 获取输入输出IO状态
/// @return 
bool PassiveStateMachine::Get_State() {
    if(serial_port_)
    {
        std::string response;
        bool ret = serial_port_->sendData("#016\r");
        if(!ret)
        {
            std::cout << "Send data failed" << std::endl;
            return false;
        }
        ret = serial_port_->readData(response);
        if(!ret)
        {
            std::cout << "Read data failed" << std::endl;
            return false;
        }
        adam_data_ = parse_adam_4055_response(response);
        adam_data_.print();
        return true
    }
    return false;
}

/// @brief 格式化输出命令，将状态转换为字符串
/// @param signal 输出信号类型
/// @param state 信号状态
/// @return 格式化后的命令字符串
std::string PassiveStateMachine::formatOutputCommand(Output_Signal signal, int state) {
    return std::string(signal) + (state == ON ? "1" : "0");
}
bool PassiveStateMachine::Set_L_REQ(int state) {
    if(serial_port_)
    {
        std::string response;
        bool ret = serial_port_->sendData(formatOutputCommand(Output_Signal::L_REQ, state));
        if(!ret)
        {
            std::cout << "Send data failed" << std::endl;
            return false;
        }
        ret = serial_port_->readData(response);
        if(!ret)
        {
            std::cout << "Read data failed" << std::endl;
            return false;
        }
        ret = is_adam_output_ack(response);
        if(!ret)
        {
            std::cout << "Read data failed" << std::endl;
            return false;
        }
        return true;
    }
    return false;
}

bool PassiveStateMachine::Set_READY(int state) {
    if(serial_port_)
    {
        std::string response;
        bool ret = serial_port_->sendData(formatOutputCommand(Output_Signal::READY, state));
        if(!ret)
        {
            std::cout << "Send data failed" << std::endl;
            return false;
        }
        ret = serial_port_->readData(response);
        if(!ret)
        {
            std::cout << "Read data failed" << std::endl;
        }
        ret = is_adam_output_ack(response);
        if(!ret)
        {
            std::cout << "Read data failed" << std::endl;
            return false;
        }
        return true;
    }
    return false;
}

void PassiveStateMachine::Process_State_logic()
{
    switch (current_state_)
    {
    case State::IDLE:
        if (Get_State())
        {
            if (adam_data_.inputStates[static_cast<int>(Input_Signal::VALID)] && adam_data_.inputStates[static_cast<int>(Input_Signal::CS_0)])
            {
                current_state_ = State::P_WAITING_FOR_AGV_REQUEST_CMD;
            }
        }
        else
        {
            current_state_ = State::P_ERROR_STATE;
            std::cout << "Get state failed" << std::endl;
        }
        break;
    case State::P_WAITING_FOR_AGV_REQUEST_CMD:
        if (Set_L_REQ(ON))
        {
            if (Get_State)
            {
                if (adam_data_.inoutputStates[static_cast<int>(Input_Signal::TR_REQ)])
                {
                    current_state_ = State::P_PREPARING_TRANSFER;
                }
                else
                {
                    current_state_ = State::P_ERROR_STATE;
                }
            }
            else
            {
                current_state_ = State::P_ERROR_STATE;
                std::cout << "Get state failed" << std::endl;
            }
        }
        else
        {
            current_state_ = State::P_ERROR_STATE;
            std::cout << "Set L_REQ failed" << std::endl;
        }
        break;
    case State::P_PREPARING_TRANSFER:
        if (Set_READY(ON))
        {
            if (Get_State)
            {
                if (adam_data_.inoutputStates[static_cast<int>(Input_Signal::BUSY)])
                {
                    current_state_ = State::P_MONITORING_TRANSFER;
                }
                else
                {
                    current_state_ = State::P_ERROR_STATE;
                }
            }
            else
            {
                current_state_ = State::P_ERROR_STATE;
                std::cout << "Get state failed" << std::endl;
            }
        }
        else
        {
            current_state_ = State::P_ERROR_STATE;
            std::cout << "Set READY failed" << std::endl;
        }
        break;
    case State::P_MONITORING_TRANSFER:
        if (set_L_REQ(OFF))
        {
            if (Get_State)
            {
                if (adam_data_.inoutputStates[static_cast<int>(Input_Signal::BUSY)] &&
                    adam_data_.inoutputStates[static_cast<int>(Input_Signal::COMPT)] &&
                    adam_data_.inoutputStates[static_cast<int>(Input_Signal::TR_REQ)])
                {
                    current_state_ = State::P_WAITING_FOR_FINAL_AGV_CLEANUP;
                }
                else
                {
                    current_state_ = State::P_ERROR_STATE;
                }
            }
            else
            {
                current_state_ = State::P_ERROR_STATE;
                std::cout << "Get state failed" << std::endl;
            }
        }
        else
        {
            current_state_ = State::P_ERROR_STATE;
            std::cout << "Set L_REQ failed" << std::endl;
        }
        break;
    case State::P_WAITING_FOR_FINAL_AGV_CLEANUP:
        if (set_READY(OFF))
        {
            if (Get_State)
            {
                if (adam_data_.inoutputStates[static_cast<int>(Input_Signal::COMPT)] &&
                    adam_data_.inoutputStates[static_cast<int>(Input_Signal::VALID)] &&
                    adam_data_.inoutputStates[static_cast<int>(Input_Signal::CS_0)])
                {
                    current_state_ = State::IDLE;
                }
                else
                {
                    current_state_ = State::P_ERROR_STATE;
                }
            }
            else
            {
                current_state_ = State::P_ERROR_STATE;
                std::cout << "Get state failed" << std::endl;
            }
        }
        else
        {
            current_state_ = State::P_ERROR_STATE;
            std::cout << "Set READY failed" << std::endl;
        }
        break;
    default:
        break;
    }
}