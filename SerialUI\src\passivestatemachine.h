#ifndef PASSIVESTATEMACHINE_H
#define PASSIVESTATEMACHINE_H

#include <string>
#include <memory>
#include "../qserial/src/service.h"


class PassiveStateMachine
{
public:
    enum class Input_Signal
    {
        VALID = 0,
        CS_0,
        CS_1,
        NC_1
        TR_REQ,
        BUSY,
        COMPT,
        CONT
    };
    enum class Output_Signal
    {
        L_REQ   = "#01100",
        U_REQ   = "#01110",
        NC_1    = "#01120",
        READY   = "#01130",
        NC_2    = "#01140",
        NC_3    = "#01150",
        HO_AVBL = "#01160",
        ES      = "#01170"
    };
    enum class State
    {
        IDLE,
        P_WAITING_FOR_AGV_REQUEST_CMD,   // Got CS0, VALID
        P_PREPARING_TRANSFER,            // Got L_REQ/TR_REQ CMD
        P_READY_WAITING_FOR_AGV_BUSY,    // Sent PASSIVE_READY_STATUS ON
        P_MONITORING_TRANSFER,           // AGV is BUSY
        P_WAITING_FOR_FINAL_AGV_CLEANUP, // Sent PASSIVE_TRANSFER_COMPLETED & PASSIVE_READY_OFF
        P_ERROR_STATE
    };
    // 存储解析结果的结构体
struct Adam4055Data {
    bool commandWasValid = false; // 响应是否以 '!' 开始
    bool isErrorResponse = false; // 响应是否以 '?' 开始
    std::string moduleAddress;
    std::vector<bool> outputStates; // 8个DO状态 (CH0-CH7)
    std::vector<bool> inputStates;  // 8个DI状态 (CH0-CH7)
    std::string rawResponse;
    std::string errorMessage;

    void print() const {
        std::cout << "--- Parsed ADAM-4055 Response ---" << std::endl;
        std::cout << "Raw Response: '" << rawResponse << "'" << std::endl;
        if (!errorMessage.empty()) {
            std::cout << "Error: " << errorMessage << std::endl;
            // 即使有错误，也可能解析出部分信息
        }
        std::cout << "Module Address: " << moduleAddress << std::endl;
        std::cout << "Command Valid (indicated by '!'): " << (commandWasValid ? "Yes" : "No") << std::endl;
        std::cout << "Is Error Response (indicated by '?'): " << (isErrorResponse ? "Yes" : "No") << std::endl;

        if (commandWasValid && !isErrorResponse) {
            if (!outputStates.empty()) {
                std::cout << "Output States (DO0 to DO7): ";
                for (size_t i = 0; i < outputStates.size(); ++i) {
                    std::cout << "CH" << i << "=" << (outputStates[i] ? "ON " : "OFF ");
                }
                std::cout << std::endl;
            }
            if (!inputStates.empty()) {
                std::cout << "Input States (DI0 to DI7): ";
                for (size_t i = 0; i < inputStates.size(); ++i) {
                    std::cout << "CH" << i << "=" << (inputStates[i] ? "HIGH " : "LOW ");
                }
                std::cout << std::endl;
            }
        }
        std::cout << "---------------------------------" << std::endl;
    }
};

private:
    State current_state_;
    const int timeout_ = 1000;
    std::shared_ptr<SerialService> serial_port_; // 串口服务对象
    Adam4055Data adam_data_;

public:
    PassiveStateMachine(std::shared_ptr<SerialService> serial_port);
    ~PassiveStateMachine();

    State getCurrentState() const{ return current_state_; };
    std::string getCurrentStateName() const;
    Adam4055Data parse_adam_4055_response(const std::string& response_str_with_cr);
    std::vector<bool> hex_byte_string_to_8bits(const std::string& hex_byte_str);
};

#endif // PASSIVESTATEMACHINE_H
